import express, { Router } from 'express';
import fs from 'fs';
import { UploadConstant } from '../utils/upload_constant';
const router = Router();

/**
 * @swagger
 * /invoice/{filename}:
 *   get:
 *     tags:
 *       - Media
 *     summary: Download invoice file
 *     description: Download invoice PDF file
 *     parameters:
 *       - name: filename
 *         in: path
 *         description: Invoice file name
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Invoice file
 *         content:
 *           application/pdf:
 *             schema:
 *               type: string
 *               format: binary
 *       400:
 *         description: File not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/invoice/:name', (req, res) => {
    const fileName = req.params.name;
    try {
        const file = fs.readFileSync(`${UploadConstant.UPLOAD_DIR_INVOICE}/${fileName}`);
        // res.writeHead(200, { 'Content-Type': 'image/jpg' });
        res.end(file, 'binary');
    } catch (err: any) {
        res.status(400).send(err.message);
    }
});

export default router;

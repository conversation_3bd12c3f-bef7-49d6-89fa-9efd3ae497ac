module.exports = {
    parser: '@typescript-eslint/parser',
    parserOptions: {
        ecmaVersion: 2020,
        sourceType: 'module',
    },
    plugins: ['@typescript-eslint'],
    extends: [
        'eslint:recommended',
    ],
    rules: {
        // Basic rules
        'no-console': 'off',
        'no-unused-vars': 'off',

        // Code style
        'prefer-const': 'error',
        'no-var': 'error',

        // Best practices
        'eqeqeq': ['error', 'always'],
        'curly': ['error', 'all'],
        'no-eval': 'error',
        'no-implied-eval': 'error',
        'no-new-func': 'error',
        'no-script-url': 'error',
    },
    env: {
        node: true,
        es6: true,
    },
    ignorePatterns: ['dist/', 'node_modules/', '*.js'],
};

import swaggerJSDoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';
import { Express } from 'express';

// Swagger JSDoc configuration
const swaggerOptions = {
    definition: {
        openapi: '3.0.3',
        info: {
            title: 'Whitelotus Assignment API',
            description: 'Invoice Management API for invoice management and user authentication',
            version: '1.0.0',
            contact: {
                name: 'Fintech Admin',
                email: '<EMAIL>'
            },
            license: {
                name: 'ISC'
            }
        },
        servers: [
            {
                url: 'http://localhost:3000/api',
                description: 'Development server'
            },
            {
                url: 'https://api.whitelotus.com/api',
                description: 'Production server'
            }
        ],
        components: {
            securitySchemes: {
                bearerAuth: {
                    type: 'http',
                    scheme: 'bearer',
                    bearerFormat: 'JWT',
                    description: 'JWT token for authentication'
                }
            },
            schemas: {
                LoginRequest: {
                    type: 'object',
                    required: ['email', 'password'],
                    properties: {
                        email: {
                            type: 'string',
                            format: 'email',
                            description: 'User email address',
                            example: '<EMAIL>'
                        },
                        password: {
                            type: 'string',
                            description: 'User password',
                            example: 'Pass@1234'
                        }
                    }
                },
                CreateInvoiceRequest: {
                    type: 'object',
                    required: ['client_name', 'amount', 'status', 'due_date', 'invoice_file_name'],
                    properties: {
                        client_name: {
                            type: 'string',
                            description: 'Name of the client',
                            example: 'John Doe'
                        },
                        amount: {
                            type: 'number',
                            format: 'decimal',
                            description: 'Invoice amount',
                            example: 1500.5
                        },
                        status: {
                            type: 'string',
                            enum: ['PENDING', 'PAID', 'OVERDUE'],
                            description: 'Invoice status',
                            example: 'PENDING'
                        },
                        due_date: {
                            type: 'string',
                            format: 'date',
                            description: 'Invoice due date',
                            example: '2024-12-31'
                        },
                        invoice_file_name: {
                            type: 'string',
                            description: 'Name of the invoice file',
                            example: 'invoice_123.pdf'
                        }
                    }
                },
                Invoice: {
                    type: 'object',
                    properties: {
                        id: {
                            type: 'string',
                            format: 'uuid',
                            description: 'Unique invoice identifier'
                        },
                        invoice_number: {
                            type: 'string',
                            description: 'Auto-generated invoice number'
                        },
                        client_name: {
                            type: 'string',
                            description: 'Name of the client'
                        },
                        amount: {
                            type: 'number',
                            format: 'decimal',
                            description: 'Invoice amount'
                        },
                        status: {
                            type: 'string',
                            enum: ['PENDING', 'PAID', 'OVERDUE'],
                            description: 'Invoice status'
                        },
                        due_date: {
                            type: 'string',
                            format: 'date-time',
                            description: 'Invoice due date'
                        },
                        invoice_file_name: {
                            type: 'string',
                            description: 'Name of the invoice file'
                        },
                        invoice_file_url: {
                            type: 'string',
                            format: 'uri',
                            description: 'URL to download the invoice file'
                        },
                        is_active: {
                            type: 'boolean',
                            description: 'Whether the invoice is active'
                        },
                        createdAt: {
                            type: 'string',
                            format: 'date-time'
                        },
                        updatedAt: {
                            type: 'string',
                            format: 'date-time'
                        }
                    }
                },
                LoginResponse: {
                    type: 'object',
                    properties: {
                        status: {
                            type: 'integer',
                            example: 200
                        },
                        message: {
                            type: 'string',
                            example: 'User loggedIn successfully'
                        },
                        data: {
                            type: 'object',
                            properties: {
                                id: {
                                    type: 'string',
                                    format: 'uuid',
                                    example: '123e4567-e89b-12d3-a456-************'
                                },
                                first_name: {
                                    type: 'string',
                                    example: 'Admin'
                                },
                                last_name: {
                                    type: 'string',
                                    example: 'Fintech'
                                },
                                email: {
                                    type: 'string',
                                    format: 'email',
                                    example: '<EMAIL>'
                                },
                                is_active: {
                                    type: 'boolean',
                                    example: true
                                },
                                token: {
                                    type: 'string',
                                    description: 'JWT authentication token',
                                    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
                                },
                                createdAt: {
                                    type: 'string',
                                    format: 'date-time',
                                    example: '2023-01-01T00:00:00.000Z'
                                },
                                updatedAt: {
                                    type: 'string',
                                    format: 'date-time',
                                    example: '2023-01-01T00:00:00.000Z'
                                }
                            }
                        }
                    }
                },
                InvoiceResponse: {
                    type: 'object',
                    properties: {
                        status: {
                            type: 'integer',
                            example: 200
                        },
                        message: {
                            type: 'string',
                            example: 'Invoice created successfully'
                        },
                        data: {
                            $ref: '#/components/schemas/Invoice'
                        }
                    }
                },
                InvoiceListResponse: {
                    type: 'object',
                    properties: {
                        status: {
                            type: 'integer',
                            example: 200
                        },
                        message: {
                            type: 'string',
                            example: 'Invoices fetched successfully'
                        },
                        data: {
                            type: 'array',
                            items: {
                                $ref: '#/components/schemas/Invoice'
                            }
                        }
                    }
                },
                ErrorResponse: {
                    type: 'object',
                    properties: {
                        status: {
                            type: 'integer',
                            description: 'HTTP status code'
                        },
                        message: {
                            type: 'string',
                            description: 'Error message'
                        },
                        error: {
                            type: 'object',
                            description: 'Detailed error information'
                        }
                    }
                }
            }
        },
        tags: [
            {
                name: 'Authentication',
                description: 'User authentication endpoints'
            },
            {
                name: 'Invoices',
                description: 'Invoice management endpoints'
            },
            {
                name: 'Media',
                description: 'File download endpoints'
            }
        ]
    },
    apis: ['./dist/src/controllers/*.js', './dist/src/routes/*.js'] // Path to the API files
};

// Generate swagger specification
const swaggerDocument = swaggerJSDoc(swaggerOptions);

// Swagger UI options
const swaggerUiOptions = {
    customCss: '.swagger-ui .topbar { display: none }',
    customSiteTitle: 'Whitelotus Assignment API Documentation',
    customfavIcon: '/favicon.ico',
    swaggerOptions: {
        persistAuthorization: true,
        displayRequestDuration: true,
        docExpansion: 'none',
        filter: true,
        showExtensions: true,
        showCommonExtensions: true,
        tryItOutEnabled: true
    }
};

/**
 * Setup Swagger documentation for the Express app
 * @param app Express application instance
 */
export const setupSwagger = (app: Express): void => {
    // Serve Swagger UI at /api-docs
    app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerDocument, swaggerUiOptions));

    // Serve raw OpenAPI spec at /api-docs.json
    app.get('/api-docs.json', (_req, res) => {
        res.setHeader('Content-Type', 'application/json');
        res.send(swaggerDocument);
    });

    // Serve raw OpenAPI spec at /api-docs.yaml
    app.get('/api-docs.yaml', (_req, res) => {
        res.setHeader('Content-Type', 'text/yaml');
        res.send(JSON.stringify(swaggerDocument, null, 2));
    });

    console.log('📚 Swagger documentation available at:');
    console.log('   - Swagger UI: http://localhost:3000/api-docs');
    console.log('   - OpenAPI JSON: http://localhost:3000/api-docs.json');
    console.log('   - OpenAPI YAML: http://localhost:3000/api-docs.yaml');
};

export default swaggerDocument;
